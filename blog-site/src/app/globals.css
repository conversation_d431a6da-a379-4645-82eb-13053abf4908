@import "tailwindcss";

:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */

    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Custom utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hindi font support */
.hindi-text {
  font-family: 'Noto Sans Devanagari', 'Mangal', 'Kruti Dev 010', Arial, sans-serif;
}

/* Wireframe-specific styles */
.wireframe-box {
    border: 2px dashed var(--color-border);
    background-color: var(--color-bg-1);
    border-radius: var(--radius-base);
}

/* Header Styles */
.header {
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    padding: var(--space-16) 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo-text {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin: 0;
}

.nav-menu {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-24);
}

.nav-link {
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    transition: color var(--duration-fast) var(--ease-standard);
}

.nav-link:hover {
    color: var(--color-primary);
}

.header-actions {
    gap: var(--space-16);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    padding: var(--space-8) var(--space-12);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-base);
    font-size: var(--font-size-sm);
    width: 200px;
    background-color: var(--color-surface);
    color: var(--color-text);
}

.search-btn {
    position: absolute;
    right: var(--space-8);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-md);
}

.lang-toggle {
    white-space: nowrap;
}

/* Hero Section */
.hero {
    background-color: var(--color-bg-2);
    padding: var(--space-32) 0;
}

.hero-banner {
    display: flex;
    align-items: center;
    gap: var(--space-32);
    max-width: 800px;
    margin: 0 auto;
}

.hero-image-placeholder {
    width: 300px;
    height: 200px;
    background-color: var(--color-bg-3);
    border: 2px dashed var(--color-border);
    border-radius: var(--radius-lg);
    flex-shrink: 0;
    position: relative;
}

.hero-image-placeholder::after {
    content: "Featured Image";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

.hero-text {
    flex: 1;
}

.hero-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-12);
    color: var(--color-text);
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-20);
}

/* Main Content Layout */
.main-content {
    padding: var(--space-32) 0;
}

.content-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-32);
}

.section-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-24);
    color: var(--color-text);
    text-align: center;
}

/* Shayari Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-20);
    margin-bottom: var(--space-32);
}

.shayari-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
    box-shadow: var(--shadow-sm);
    transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.shayari-card:hover {
    box-shadow: var(--shadow-md);
}

.card-image-placeholder {
    width: 100%;
    height: 150px;
    background-color: var(--color-bg-4);
    border: 2px dashed var(--color-border);
    border-radius: var(--radius-base);
    margin-bottom: var(--space-16);
    position: relative;
}

.card-image-placeholder::after {
    content: "Shayari Image";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-8);
    color: var(--color-text);
}

.card-author {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-12);
}

.card-excerpt {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--color-text);
    margin-bottom: var(--space-16);
}

.card-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-12);
    margin-bottom: var(--space-16);
}

.card-category {
    background-color: var(--color-bg-5);
    color: var(--color-text);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.card-date {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

.card-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-engagement {
    display: flex;
    align-items: center;
    gap: var(--space-16);
}

.like-count, .share-btn {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    background: none;
    border: none;
    cursor: pointer;
}

.like-count:hover, .share-btn:hover {
    color: var(--color-primary);
}

.load-more-container {
    text-align: center;
    margin-top: var(--space-32);
}

/* Sidebar Styles */
.sidebar {
    display: none;
}

.widget {
    background-color: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
    margin-bottom: var(--space-20);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-16);
    color: var(--color-text);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: var(--space-8);
}

.popular-item, .recent-item {
    padding: var(--space-12) 0;
    border-bottom: 1px solid var(--color-card-border-inner);
}

.popular-item:last-child, .recent-item:last-child {
    border-bottom: none;
}

.popular-item h5 {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-4);
    color: var(--color-text);
}

.popular-author {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: 0 0 var(--space-4) 0;
}

.popular-likes {
    font-size: var(--font-size-sm);
    color: var(--color-primary);
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
}

.category-link {
    padding: var(--space-8) var(--space-12);
    background-color: var(--color-bg-6);
    border-radius: var(--radius-base);
    color: var(--color-text);
    font-size: var(--font-size-sm);
    transition: background-color var(--duration-fast) var(--ease-standard);
}

.category-link:hover {
    background-color: var(--color-secondary-hover);
}

.category-link.active {
    background-color: var(--color-primary);
    color: var(--color-btn-primary-text);
}

.recent-item {
    display: flex;
    gap: var(--space-12);
    align-items: center;
}

.recent-image-placeholder {
    width: 60px;
    height: 60px;
    background-color: var(--color-bg-7);
    border: 1px dashed var(--color-border);
    border-radius: var(--radius-base);
    flex-shrink: 0;
}

.recent-content h5 {
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-4);
    color: var(--color-text);
}

.recent-date {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    margin: 0;
}

.author-spotlight {
    text-align: center;
}

.author-image-placeholder {
    width: 80px;
    height: 80px;
    background-color: var(--color-bg-8);
    border: 2px dashed var(--color-border);
    border-radius: 50%;
    margin: 0 auto var(--space-16) auto;
}

.author-spotlight h5 {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-8);
    color: var(--color-text);
}

.author-bio {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-16);
}

.newsletter-signup p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-12);
}

/* Footer Styles */
.footer {
    background-color: var(--color-surface);
    border-top: 1px solid var(--color-border);
    padding: var(--space-32) 0 var(--space-16) 0;
    margin-top: var(--space-32);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-24);
    margin-bottom: var(--space-24);
}

.footer-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-12);
    color: var(--color-text);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: var(--space-8);
}

.footer-links a {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    transition: color var(--duration-fast) var(--ease-standard);
}

.footer-links a:hover {
    color: var(--color-primary);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
}

.social-link {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    transition: color var(--duration-fast) var(--ease-standard);
}

.social-link:hover {
    color: var(--color-primary);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--space-16);
    border-top: 1px solid var(--color-border);
}

.footer-bottom p {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-8);
}

/* Responsive Design */
@media (min-width: 640px) {
    .search-input {
        width: 250px;
    }

    .cards-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-banner {
        gap: var(--space-24);
    }

    .hero-image-placeholder {
        width: 250px;
        height: 170px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .social-links {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

@media (min-width: 768px) {
    .nav-menu {
        display: block;
    }

    .content-layout {
        grid-template-columns: 1fr 300px;
    }

    .sidebar {
        display: block;
    }

    .section-title {
        text-align: left;
    }

    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }

    .hero-banner {
        gap: var(--space-32);
    }

    .hero-image-placeholder {
        width: 300px;
        height: 200px;
    }
}

@media (min-width: 1024px) {
    .cards-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .content-layout {
        grid-template-columns: 1fr 320px;
        gap: var(--space-32);
    }

    .search-input {
        width: 300px;
    }
}

/* Mobile-first enhancements */
@media (max-width: 767px) {
    .header-actions {
        flex-direction: column;
        gap: var(--space-8);
    }

    .search-input {
        width: 180px;
    }

    .hero-banner {
        flex-direction: column;
        text-align: center;
    }

    .hero-image-placeholder {
        width: 100%;
        max-width: 300px;
        height: 200px;
    }

    .card-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-actions {
        flex-direction: column;
        gap: var(--space-12);
        align-items: flex-start;
    }
}
