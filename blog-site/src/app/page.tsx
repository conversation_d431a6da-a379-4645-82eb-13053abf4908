import { db } from '@/lib/database';
import PostCard from '@/components/PostCard';

export default async function Home() {
  const posts = await db.getAllPosts();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              हिंदी ब्लॉग
            </h1>
            <p className="text-lg text-gray-600">
              शायरी, प्रेरणादायक विचार और जीवन की खुशियों का संग्रह
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {posts.length === 0 ? (
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              कोई पोस्ट उपलब्ध नहीं है
            </h2>
            <p className="text-gray-600">
              जल्द ही नई पोस्ट्स आएंगी। कृपया बाद में देखें।
            </p>
          </div>
        ) : (
          <>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                नवीनतम पोस्ट्स
              </h2>
              <p className="text-gray-600">
                {posts.length} पोस्ट्स उपलब्ध हैं
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {posts.map((post) => (
                <PostCard key={post.id} post={post} />
              ))}
            </div>
          </>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2025 हिंदी ब्लॉग। सभी अधिकार सुरक्षित।</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
