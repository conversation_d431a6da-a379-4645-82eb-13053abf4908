'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import ShayariCard from '@/components/ShayariCard';
import Sidebar from '@/components/Sidebar';
import Footer from '@/components/Footer';

// Authentic Hindi Shayari content
const sampleShayari = [
  {
    id: 1,
    title: "दिल की बात",
    author: "राहुल शर्मा",
    category: "प्रेम शायरी",
    excerpt: "जब से तुम मिले हो, जिंदगी में रंग आ गया है,\nहर सुबह नई लगती है, हर शाम प्यारी लगती है।\nतुम्हारी मुस्कान में छुपा है मेरा जहान,\nतुम्हारे बिना अधूरा लगता है हर एक दिन।",
    fullContent: "जब से तुम मिले हो, जिंदगी में रंग आ गया है,\nहर सुबह नई लगती है, हर शाम प्यारी लगती है।\n\nतुम्हारी मुस्कान में छुपा है मेरा जहान,\nतुम्हारे बिना अधूरा लगता है हर एक दिन।\n\nप्रेम की इस राह में, तुमसे मिला है सुकून,\nदिल की हर धड़कन में, बसा है तुम्हारा जुनून।\n\nकहते हैं लोग कि मोहब्बत अंधी होती है,\nपर मैंने तो तुम्हें देखकर ही जिंदगी पाई है।",
    likes: 245,
    date: "15 जनवरी 2025"
  },
  {
    id: 2,
    title: "टूटे सपनों की कहानी",
    author: "प्रिया गुप्ता",
    category: "दुख शायरी",
    excerpt: "कुछ सपने टूटकर भी खुशियाँ दे जाते हैं,\nकुछ रिश्ते छूटकर भी यादें दे जाते हैं।\nजो खो गया वो वापस नहीं आता,\nपर जो मिला है उसकी कदर करना सिखाता है।",
    fullContent: "कुछ सपने टूटकर भी खुशियाँ दे जाते हैं,\nकुछ रिश्ते छूटकर भी यादें दे जाते हैं।\n\nजो खो गया वो वापस नहीं आता,\nपर जो मिला है उसकी कदर करना सिखाता है।\n\nआंसुओं में भी छुपी होती है मुस्कान,\nदुख के बाद ही आती है खुशियों की पहचान।\n\nटूटे दिल से भी निकलती है प्रेम की आवाज,\nजिंदगी का यही तो है सबसे बड़ा राज।",
    likes: 189,
    date: "12 जनवरी 2025"
  },
  {
    id: 3,
    title: "हौसलों का जमाना",
    author: "अमित कुमार",
    category: "मोटिवेशनल शायरी",
    excerpt: "हार कर भी जीतने का जुनून रखना,\nमुश्किलों से भी दोस्ती का सुकून रखना।\nसपने वो नहीं जो सोते वक्त आते हैं,\nसपने वो हैं जो सोने नहीं देते हैं।",
    fullContent: "हार कर भी जीतने का जुनून रखना,\nमुश्किलों से भी दोस्ती का सुकून रखना।\n\nसपने वो नहीं जो सोते वक्त आते हैं,\nसपने वो हैं जो सोने नहीं देते हैं।\n\nगिरकर उठना ही जिंदगी का असल मतलब है,\nहर नई सुबह एक नया मौका लेकर आती है।\n\nहिम्मत हार जाना सबसे बड़ी हार है,\nउम्मीद रखना सबसे बड़ी जीत है।",
    likes: 356,
    date: "10 जनवरी 2025"
  },
  {
    id: 4,
    title: "दोस्ती का रिश्ता",
    author: "सुनीता देवी",
    category: "दोस्ती शायरी",
    excerpt: "सच्चे दोस्त वो होते हैं जो मुश्किल वक्त में साथ खड़े रहते हैं,\nबिना कहे समझ जाते हैं, बिना मांगे दे जाते हैं।\nदोस्ती का रिश्ता खून से नहीं दिल से बनता है,\nयह वो खुशी है जो बांटने से बढ़ती जाती है।",
    fullContent: "सच्चे दोस्त वो होते हैं जो मुश्किल वक्त में साथ खड़े रहते हैं,\nबिना कहे समझ जाते हैं, बिना मांगे दे जाते हैं।\n\nदोस्ती का रिश्ता खून से नहीं दिल से बनता है,\nयह वो खुशी है जो बांटने से बढ़ती जाती है।\n\nसाथ हंसना, साथ रोना, यही तो है दोस्ती,\nजिंदगी की हर खुशी में शामिल होना, यही तो है दोस्ती।\n\nदोस्त वो नहीं जो हमेशा तारीफ करे,\nदोस्त वो है जो गलत राह से रोक दे।",
    likes: 298,
    date: "8 जनवरी 2025"
  },
  {
    id: 5,
    title: "जिंदगी के रंग",
    author: "विकास पांडे",
    category: "जिंदगी शायरी",
    excerpt: "हर दिन नया होता है, हर पल एक नई शुरुआत,\nजिंदगी का मतलब है हर लम्हे की बात।\nकभी धूप, कभी छांव, यही तो है जिंदगी,\nकभी खुशी, कभी गम, यही तो है जिंदगी।",
    fullContent: "हर दिन नया होता है, हर पल एक नई शुरुआत,\nजिंदगी का मतलब है हर लम्हे की बात।\n\nकभी धूप, कभी छांव, यही तो है जिंदगी,\nकभी खुशी, कभी गम, यही तो है जिंदगी।\n\nवक्त के साथ चलना सीखो, ठहरना नहीं आता,\nजो बीत गया उसे भूलो, आगे का रास्ता दिखाता।\n\nजिंदगी एक किताब है, हर दिन एक नया पन्ना,\nकुछ यादें मिठास भरी, कुछ सिखाने वाली कहानी।",
    likes: 412,
    date: "5 जनवरी 2025"
  },
  {
    id: 6,
    title: "प्रेम की गहराई",
    author: "राहुल शर्मा",
    category: "रोमांटिक शायरी",
    excerpt: "तुम्हारी मोहब्बत में डूबकर हमने जीना सीखा है,\nतुम्हारे प्यार में पागल होकर हमने प्रेम को समझा है।\nतुम्हारी आंखों में छुपा है मेरा आसमान,\nतुम्हारे दिल में बसा है मेरा इमान।",
    fullContent: "तुम्हारी मोहब्बत में डूबकर हमने जीना सीखा है,\nतुम्हारे प्यार में पागल होकर हमने प्रेम को समझा है।\n\nतुम्हारी आंखों में छुपा है मेरा आसमान,\nतुम्हारे दिल में बसा है मेरा इमान।\n\nप्रेम की इस राह में कोई मंजिल नहीं,\nबस तुम्हारे साथ चलने का एहसास है।\n\nतुम्हारे बिना अधूरा है हर एक सपना,\nतुम्हारे साथ पूरा है जिंदगी का हर रंग।",
    likes: 523,
    date: "2 जनवरी 2025"
  }
];

export default function Home() {
  const [shayariData, setShayariData] = useState(sampleShayari);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredShayari, setFilteredShayari] = useState(sampleShayari);

  useEffect(() => {
    let filtered = shayariData;

    if (selectedCategory !== 'all') {
      filtered = shayariData.filter(shayari =>
        shayari.category === selectedCategory
      );
    }

    setFilteredShayari(filtered);
  }, [selectedCategory, shayariData]);

  const handleLike = (id: number) => {
    setShayariData(prev =>
      prev.map(shayari =>
        shayari.id === id
          ? { ...shayari, likes: shayari.likes + 1 }
          : shayari
      )
    );
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  const handleLoadMore = () => {
    // Simulate loading more content
    const additionalShayari = [
      {
        id: shayariData.length + 1,
        title: "खुशियों का मंजर",
        author: "सुनीता देवी",
        category: "जिंदगी शायरी",
        excerpt: "हर दिन में छुपी होती है एक नई खुशी...",
        likes: 167,
        date: "28 जनवरी 2025"
      },
      {
        id: shayariData.length + 2,
        title: "यादों का सफर",
        author: "विकास पांडे",
        category: "दुख शायरी",
        excerpt: "कुछ यादें दिल में बस जाती हैं और फिर कभी नहीं जातीं...",
        likes: 234,
        date: "25 जनवरी 2025"
      },
      {
        id: shayariData.length + 3,
        title: "सपनों की उड़ान",
        author: "प्रिया गुप्ता",
        category: "मोटिवेशनल शायरी",
        excerpt: "सपने वो नहीं जो सोते वक्त आते हैं, सपने वो हैं जो सोने नहीं देते...",
        likes: 445,
        date: "22 जनवरी 2025"
      }
    ];

    setShayariData(prev => [...prev, ...additionalShayari]);
    alert('नई शायरियाँ लोड हो गईं!');
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
      <Header />
      <Hero />

      {/* Main Content Area */}
      <main className="main-content">
        <div className="container">
          <div className="content-layout">
            {/* Shayari Cards Grid */}
            <div className="shayari-grid">
              <h3 className="section-title">Latest शायरी</h3>

              <div className="cards-grid">
                {filteredShayari.length === 0 ? (
                  <div className="no-results" style={{ textAlign: 'center', padding: '40px', color: 'var(--color-text-secondary)', gridColumn: '1 / -1' }}>
                    <h3>कोई शायरी नहीं मिली</h3>
                    <p>कृपया अपनी खोज या फ़िल्टर बदलें</p>
                  </div>
                ) : (
                  filteredShayari.map((shayari) => (
                    <ShayariCard
                      key={shayari.id}
                      shayari={shayari}
                      onLike={handleLike}
                    />
                  ))
                )}
              </div>

              {/* Load More Button */}
              <div className="load-more-container">
                <button
                  className="btn btn--outline"
                  onClick={handleLoadMore}
                >
                  Load More शायरी
                </button>
              </div>
            </div>

            <Sidebar
              selectedCategory={selectedCategory}
              onCategoryChange={handleCategoryChange}
            />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
