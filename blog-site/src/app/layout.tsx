import type { Metada<PERSON> } from "next";
import { Inter, Noto_Sans_Devanagari } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const notoSansDevanagari = Noto_Sans_Devanagari({
  variable: "--font-hindi",
  subsets: ["devanagari"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "शायरी ब्लॉग - हिंदी कविता संग्रह",
  description: "सुंदर हिंदी शायरी और कविता का संग्रह। प्रेम, दुख, खुशी, दोस्ती और जिंदगी की बेहतरीन शायरियाँ।",
  keywords: "हिंदी शायरी, शायरी, कविता, प्रेम शायरी, दुख शायरी, मोटिवेशनल शायरी, दोस्ती शायरी",
  authors: [{ name: "शायरी ब्लॉग टीम" }],
  openGraph: {
    title: "शायरी ब्लॉग - हिंदी कविता संग्रह",
    description: "सुंदर हिंदी शायरी और कविता का संग्रह",
    type: "website",
    locale: "hi_IN",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="hi">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${inter.variable} ${notoSansDevanagari.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
