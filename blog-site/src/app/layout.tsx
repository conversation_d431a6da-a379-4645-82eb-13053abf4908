import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "शायरी ब्लॉग - Hindi Poetry Blog",
  description: "प्रेम, दुख, खुशी की अनमोल शायरियाँ - Hindi Shayari and Poetry Collection",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="hi">
      <body className={`${inter.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
