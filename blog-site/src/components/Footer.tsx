'use client';

export default function Footer() {
  const handleLinkClick = (section: string) => {
    alert(`${section} सेक्शन जल्द ही आएगा!`);
  };

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <h4 className="footer-title">श्रेणियाँ</h4>
            <ul className="footer-links">
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('प्रेम शायरी');
                  }}
                >
                  प्रेम शायरी
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('दुख शायरी');
                  }}
                >
                  दुख शायरी
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('मोटिवेशनल शायरी');
                  }}
                >
                  मोटिवेशनल शायरी
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('दोस्ती शायरी');
                  }}
                >
                  दोस्ती शायरी
                </a>
              </li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4 className="footer-title">लेखक</h4>
            <ul className="footer-links">
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('राहुल शर्मा');
                  }}
                >
                  राहुल शर्मा
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('प्रिया गुप्ता');
                  }}
                >
                  प्रिया गुप्ता
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('अमित कुमार');
                  }}
                >
                  अमित कुमार
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('सुनीता देवी');
                  }}
                >
                  सुनीता देवी
                </a>
              </li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4 className="footer-title">About</h4>
            <ul className="footer-links">
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('हमारे बारे में');
                  }}
                >
                  हमारे बारे में
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('Contact');
                  }}
                >
                  Contact
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('Privacy Policy');
                  }}
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a 
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick('Terms of Service');
                  }}
                >
                  Terms of Service
                </a>
              </li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4 className="footer-title">Follow Us</h4>
            <div className="social-links">
              <a 
                href="#" 
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleLinkClick('📘 Facebook');
                }}
              >
                📘 Facebook
              </a>
              <a 
                href="#" 
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleLinkClick('🐦 Twitter');
                }}
              >
                🐦 Twitter
              </a>  
              <a 
                href="#" 
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleLinkClick('📷 Instagram');
                }}
              >
                📷 Instagram
              </a>
              <a 
                href="#" 
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleLinkClick('📺 YouTube');
                }}
              >
                📺 YouTube
              </a>
            </div>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p>&copy; 2025 शायरी ब्लॉग. All rights reserved.</p>
          <p>Contact: <EMAIL> | +91 9876543210</p>
        </div>
      </div>
    </footer>
  );
}
