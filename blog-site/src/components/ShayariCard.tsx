'use client';

import { useState } from 'react';

interface ShayariData {
  id: number;
  title: string;
  author: string;
  category: string;
  excerpt: string;
  likes: number;
  date: string;
}

interface ShayariCardProps {
  shayari: ShayariData;
  onLike: (id: number) => void;
}

export default function ShayariCard({ shayari, onLike }: ShayariCardProps) {
  const [isLiked, setIsLiked] = useState(false);

  const handleLike = () => {
    setIsLiked(true);
    onLike(shayari.id);
    setTimeout(() => setIsLiked(false), 500);
  };

  const handleShare = () => {
    const shareText = `${shayari.title} - ${shayari.author}\n\n${shayari.excerpt}\n\n#शायरी #हिंदीशायरी`;
    
    if (navigator.share) {
      navigator.share({
        title: shayari.title,
        text: shareText,
        url: window.location.href
      }).catch(err => console.log('Error sharing:', err));
    } else {
      // Fallback for browsers that don't support Web Share API
      if (navigator.clipboard) {
        navigator.clipboard.writeText(shareText).then(() => {
          alert('शायरी कॉपी हो गई है!');
        }).catch(() => {
          alert(`शायरी शेयर करें:\n\n${shareText}`);
        });
      } else {
        alert(`शायरी शेयर करें:\n\n${shareText}`);
      }
    }
  };

  const handleReadFull = () => {
    alert(`${shayari.title}\n\nलेखक: ${shayari.author}\nश्रेणी: ${shayari.category}\nदिनांक: ${shayari.date}\n\n${shayari.excerpt}\n\n[पूर्ण शायरी यहाँ दिखाई जाएगी]\n\nयह एक वायरफ्रेम है - वास्तविक शायरी व्यू जल्द ही आएगा!`);
  };

  return (
    <div className="shayari-card">
      <div className="card-image-placeholder"></div>
      <h3 className="card-title">{shayari.title}</h3>
      <p className="card-author">लेखक: {shayari.author}</p>
      <p className="card-excerpt">{shayari.excerpt}</p>
      <div className="card-meta">
        <span className="card-category">{shayari.category}</span>
        <span className="card-date">{shayari.date}</span>
      </div>
      <div className="card-actions">
        <div className="card-engagement">
          <button 
            className="like-count"
            onClick={handleLike}
            style={{ color: isLiked ? 'var(--color-primary)' : '' }}
          >
            ❤️ <span>{shayari.likes}</span>
          </button>
          <button className="share-btn" onClick={handleShare}>
            📤 Share
          </button>
        </div>
        <button 
          className="btn btn--sm btn--primary read-full-btn" 
          onClick={handleReadFull}
        >
          पढ़ें
        </button>
      </div>
    </div>
  );
}
