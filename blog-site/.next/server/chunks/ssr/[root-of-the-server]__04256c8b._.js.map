{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b625b40b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_b625b40b-module__n-SICG__className\",\n  \"variable\": \"inter_b625b40b-module__n-SICG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b625b40b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_devanagari_76bb9a63.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_sans_devanagari_76bb9a63-module__XxoJ8q__className\",\n  \"variable\": \"noto_sans_devanagari_76bb9a63-module__XxoJ8q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_devanagari_76bb9a63.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Noto_Sans_Devanagari%22,%22arguments%22:[{%22variable%22:%22--font-hindi%22,%22subsets%22:[%22devanagari%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22]}],%22variableName%22:%22notoSansDevanagari%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Sans Devanagari', 'Noto Sans Devanagari Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,oKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,oKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,oKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/SH_2/blog-site/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Inter, Noto_Sans_Devanagari } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n});\n\nconst notoSansDevanagari = Noto_Sans_Devanagari({\n  variable: \"--font-hindi\",\n  subsets: [\"devanagari\"],\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"शायरी ब्लॉग - हिंदी कविता संग्रह\",\n  description: \"सुंदर हिंदी शायरी और कविता का संग्रह। प्रेम, दुख, खुशी, दोस्ती और जिंदगी की बेहतरीन शायरियाँ।\",\n  keywords: \"हिंदी शायरी, शायरी, कविता, प्रेम शायरी, दुख शायरी, मोटिवेशनल शायरी, दोस्ती शायरी\",\n  authors: [{ name: \"शायरी ब्लॉग टीम\" }],\n  openGraph: {\n    title: \"शायरी ब्लॉग - हिंदी कविता संग्रह\",\n    description: \"सुंदर हिंदी शायरी और कविता का संग्रह\",\n    type: \"website\",\n    locale: \"hi_IN\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"hi\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n      </head>\n      <body className={`${inter.variable} ${notoSansDevanagari.variable} antialiased`}>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAeO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;IACtC,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;;;;;;;0BAEtE,8OAAC;gBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,wJAAA,CAAA,UAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC;0BAC5E;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/SH_2/blog-site/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}