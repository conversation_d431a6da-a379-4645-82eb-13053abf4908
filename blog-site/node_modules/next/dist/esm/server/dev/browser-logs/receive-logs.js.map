{"version": 3, "sources": ["../../../../src/server/dev/browser-logs/receive-logs.ts"], "sourcesContent": ["import { cyan, dim, red, yellow } from '../../../lib/picocolors'\nimport type { Project } from '../../../build/swc/types'\nimport util from 'util'\nimport {\n  getConsoleLocation,\n  getSourceMappedStackFrames,\n  withLocation,\n  type MappingContext,\n} from './source-map'\nimport {\n  type ServerLogEntry,\n  type LogMethod,\n  type ConsoleEntry,\n  UNDEFINED_MARKER,\n} from '../../../next-devtools/shared/forward-logs-shared'\n\nexport function restoreUndefined(x: any): any {\n  if (x === UNDEFINED_MARKER) return undefined\n  if (Array.isArray(x)) return x.map(restoreUndefined)\n  if (x && typeof x === 'object') {\n    for (let k in x) {\n      x[k] = restoreUndefined(x[k])\n    }\n  }\n  return x\n}\n\nconst methods: Array<LogMethod> = [\n  'log',\n  'info',\n  'warn',\n  'debug',\n  'table',\n  'error',\n  'assert',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n]\n\nconst methodsToSkipInspect = new Set([\n  'table',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n])\n\n// we aren't overriding console, we're just making a (slightly convoluted) helper for replaying user console methods\nconst forwardConsole: typeof console = {\n  ...console,\n  ...Object.fromEntries(\n    methods.map((method) => [\n      method,\n      (...args: Array<any>) =>\n        (console[method] as any)(\n          ...args.map((arg) =>\n            methodsToSkipInspect.has(method) ||\n            typeof arg !== 'object' ||\n            arg === null\n              ? arg\n              : // we hardcode depth:Infinity to allow the true depth to be configured by the serialization done in the browser (which is controlled by user)\n                util.inspect(arg, { depth: Infinity, colors: true })\n          )\n        ),\n    ])\n  ),\n}\n\nasync function deserializeArgData(arg: any) {\n  try {\n    // we want undefined to be represented as it would be in the browser from the user's perspective (otherwise it would be stripped away/shown as null)\n    if (arg === UNDEFINED_MARKER) {\n      return restoreUndefined(arg)\n    }\n\n    return restoreUndefined(JSON.parse(arg))\n  } catch {\n    return arg\n  }\n}\n\nconst colorError = (\n  mapped: Awaited<ReturnType<typeof getSourceMappedStackFrames>>,\n  config?: {\n    prefix?: string\n    applyColor?: boolean\n  }\n) => {\n  const colorFn =\n    config?.applyColor === undefined || config.applyColor ? red : <T>(x: T) => x\n  switch (mapped.kind) {\n    case 'mapped-stack':\n    case 'stack': {\n      return (\n        (config?.prefix ? colorFn(config?.prefix) : '') +\n        `\\n${colorFn(mapped.stack)}`\n      )\n    }\n    case 'with-frame-code': {\n      return (\n        (config?.prefix ? colorFn(config?.prefix) : '') +\n        `\\n${colorFn(mapped.stack)}\\n${mapped.frameCode}`\n      )\n    }\n    // a more sophisticated version of this allows the user to config if they want ignored frames (but we need to be sure to source map them)\n    case 'all-ignored': {\n      return config?.prefix ? colorFn(config?.prefix) : ''\n    }\n    default: {\n    }\n  }\n  mapped satisfies never\n}\n\nfunction processConsoleFormatStrings(args: any[]): any[] {\n  /**\n   * this handles the case formatting is applied to the console log\n   * otherwise we will see the format specifier directly in the terminal output\n   */\n  if (args.length > 0 && typeof args[0] === 'string') {\n    const formatString = args[0]\n    if (\n      formatString.includes('%s') ||\n      formatString.includes('%d') ||\n      formatString.includes('%i') ||\n      formatString.includes('%f') ||\n      formatString.includes('%o') ||\n      formatString.includes('%O') ||\n      formatString.includes('%c')\n    ) {\n      try {\n        const formatted = util.format(...args)\n        return [formatted]\n      } catch {\n        return args\n      }\n    }\n  }\n  return args\n}\n\n// in the case of logging errors, we want to strip formatting\n// modifiers since we apply our own custom coloring to error\n// stacks and code blocks, and otherwise it would conflict\n// and cause awful output\nexport function stripFormatSpecifiers(args: any[]): any[] {\n  if (args.length === 0 || typeof args[0] !== 'string') return args\n\n  const fmtIn = String(args[0])\n  const rest = args.slice(1)\n\n  if (!fmtIn.includes('%')) return args\n\n  let fmtOut = ''\n  let argPtr = 0\n\n  for (let i = 0; i < fmtIn.length; i++) {\n    if (fmtIn[i] !== '%') {\n      fmtOut += fmtIn[i]\n      continue\n    }\n\n    if (fmtIn[i + 1] === '%') {\n      fmtOut += '%'\n      i++\n      continue\n    }\n\n    const token = fmtIn[++i]\n\n    if (!token) {\n      fmtOut += '%'\n      continue\n    }\n\n    if ('csdifoOj'.includes(token) || token === 'O') {\n      if (argPtr < rest.length) {\n        if (token === 'c') {\n          argPtr++\n        } else if (token === 'o' || token === 'O' || token === 'j') {\n          const obj = rest[argPtr++]\n          fmtOut += util.inspect(obj, { depth: 2, colors: false })\n        } else {\n          // string(...) is safe for remaining specifiers\n          fmtOut += String(rest[argPtr++])\n        }\n      }\n      continue\n    }\n\n    fmtOut += '%' + token\n  }\n\n  const result = [fmtOut]\n  if (argPtr < rest.length) {\n    result.push(...rest.slice(argPtr))\n  }\n\n  return result\n}\n\nasync function prepareFormattedErrorArgs(\n  entry: Extract<ServerLogEntry, { kind: 'formatted-error' }>,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const mapped = await getSourceMappedStackFrames(entry.stack, ctx, distDir)\n  return [colorError(mapped, { prefix: entry.prefix })]\n}\n\nasync function prepareConsoleArgs(\n  entry: Extract<ServerLogEntry, { kind: 'console' }>,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserialized = await Promise.all(\n    entry.args.map(async (arg) => {\n      if (arg.kind === 'arg') {\n        const data = await deserializeArgData(arg.data)\n        if (entry.method === 'warn' && typeof data === 'string') {\n          return yellow(data)\n        }\n        return data\n      }\n      if (!arg.stack) return red(arg.prefix)\n      const mapped = await getSourceMappedStackFrames(arg.stack, ctx, distDir)\n      return colorError(mapped, { prefix: arg.prefix, applyColor: false })\n    })\n  )\n\n  return processConsoleFormatStrings(deserialized)\n}\n\nasync function prepareConsoleErrorArgs(\n  entry: Extract<ServerLogEntry, { kind: 'any-logged-error' }>,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserialized = await Promise.all(\n    entry.args.map(async (arg) => {\n      if (arg.kind === 'arg') {\n        if (arg.isRejectionMessage) return red(arg.data)\n        return deserializeArgData(arg.data)\n      }\n      if (!arg.stack) return red(arg.prefix)\n      const mapped = await getSourceMappedStackFrames(arg.stack, ctx, distDir)\n      return colorError(mapped, { prefix: arg.prefix })\n    })\n  )\n\n  const mappedStack = await getSourceMappedStackFrames(\n    entry.consoleErrorStack,\n    ctx,\n    distDir\n  )\n\n  /**\n   * don't show the stack + codeblock when there are errors present, since:\n   * - it will look overwhelming to see 2 stacks and 2 code blocks\n   * - the user already knows where the console.error is at because we append the location\n   */\n  const location = getConsoleLocation(mappedStack)\n  if (entry.args.some((a) => a.kind === 'formatted-error-arg')) {\n    const result = stripFormatSpecifiers(deserialized)\n    if (location) {\n      result.push(dim(`(${location})`))\n    }\n    return result\n  }\n  const result = [\n    ...processConsoleFormatStrings(deserialized),\n    colorError(mappedStack),\n  ]\n  if (location) {\n    result.push(dim(`(${location})`))\n  }\n  return result\n}\n\nasync function handleTable(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserializedArgs = await Promise.all(\n    entry.args.map(async (arg: any) => {\n      if (arg.kind === 'formatted-error-arg') {\n        return { stack: arg.stack }\n      }\n      return deserializeArgData(arg.data)\n    })\n  )\n\n  const location = await (async () => {\n    if (!entry.consoleMethodStack) {\n      return\n    }\n    const frames = await getSourceMappedStackFrames(\n      entry.consoleMethodStack,\n      ctx,\n      distDir\n    )\n    return getConsoleLocation(frames)\n  })()\n\n  // we can't inline pass browser prefix, but it looks better multiline for table anyways\n  forwardConsole.log(browserPrefix)\n  forwardConsole.table(...deserializedArgs)\n  if (location) {\n    forwardConsole.log(dim(`(${location})`))\n  }\n}\n\nasync function handleTrace(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserializedArgs = await Promise.all(\n    entry.args.map(async (arg: any) => {\n      if (arg.kind === 'formatted-error-arg') {\n        if (!arg.stack) return red(arg.prefix)\n        const mapped = await getSourceMappedStackFrames(arg.stack, ctx, distDir)\n        return colorError(mapped, { prefix: arg.prefix })\n      }\n      return deserializeArgData(arg.data)\n    })\n  )\n\n  if (!entry.consoleMethodStack) {\n    forwardConsole.log(\n      browserPrefix,\n      ...deserializedArgs,\n      '[Trace unavailable]'\n    )\n    return\n  }\n\n  // TODO(rob): refactor so we can re-use result and not re-run the entire source map to avoid trivial post processing\n  const [mapped, mappedIgnored] = await Promise.all([\n    getSourceMappedStackFrames(entry.consoleMethodStack, ctx, distDir, false),\n    getSourceMappedStackFrames(entry.consoleMethodStack, ctx, distDir),\n  ])\n\n  const location = getConsoleLocation(mappedIgnored)\n  forwardConsole.log(\n    browserPrefix,\n    ...deserializedArgs,\n    `\\n${mapped.stack}`,\n    ...(location ? [`\\n${dim(`(${location})`)}`] : [])\n  )\n}\n\nasync function handleDir(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const loggableEntry = await prepareConsoleArgs(entry, ctx, distDir)\n  const consoleMethod =\n    (forwardConsole as any)[entry.method] || forwardConsole.log\n\n  if (entry.consoleMethodStack) {\n    const mapped = await getSourceMappedStackFrames(\n      entry.consoleMethodStack,\n      ctx,\n      distDir\n    )\n    const location = dim(`(${getConsoleLocation(mapped)})`)\n    const originalWrite = process.stdout.write.bind(process.stdout)\n    let captured = ''\n    process.stdout.write = (chunk) => {\n      captured += chunk\n      return true\n    }\n    try {\n      consoleMethod(...loggableEntry)\n    } finally {\n      process.stdout.write = originalWrite\n    }\n    const preserved = captured.replace(/\\r?\\n$/, '')\n    originalWrite(`${browserPrefix}${preserved} ${location}\\n`)\n    return\n  }\n  consoleMethod(browserPrefix, ...loggableEntry)\n}\n\nasync function handleDefaultConsole(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string,\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n) {\n  const loggableEntry = await prepareConsoleArgs(entry, ctx, distDir)\n  const withStackEntry = await withLocation(\n    {\n      original: loggableEntry,\n      stack: (entry as any).consoleMethodStack || null,\n    },\n    ctx,\n    distDir,\n    config\n  )\n  const consoleMethod = forwardConsole[entry.method] || forwardConsole.log\n  ;(consoleMethod as (...args: any[]) => void)(browserPrefix, ...withStackEntry)\n}\n\nexport async function handleLog(\n  entries: ServerLogEntry[],\n  ctx: MappingContext,\n  distDir: string,\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n): Promise<void> {\n  const browserPrefix = cyan('[browser]')\n\n  for (const entry of entries) {\n    try {\n      switch (entry.kind) {\n        case 'console': {\n          switch (entry.method) {\n            case 'table': {\n              // timeout based abort on source mapping result\n              await handleTable(entry, browserPrefix, ctx, distDir)\n              break\n            }\n            // ignore frames\n            case 'trace': {\n              await handleTrace(entry, browserPrefix, ctx, distDir)\n              break\n            }\n            case 'dir': {\n              await handleDir(entry, browserPrefix, ctx, distDir)\n              break\n            }\n            // xml log thing maybe needs an impl\n\n            // [browser] undefined (app/page.tsx:8:11) console.group\n            // check console assert\n            default: {\n              await handleDefaultConsole(\n                entry,\n                browserPrefix,\n                ctx,\n                distDir,\n                config\n              )\n            }\n          }\n          break\n        }\n        // any logged errors are anything that are logged as \"red\" in the browser but aren't only an Error (console.error, Promise.reject(100))\n        case 'any-logged-error': {\n          const consoleArgs = await prepareConsoleErrorArgs(entry, ctx, distDir)\n          forwardConsole.error(browserPrefix, ...consoleArgs)\n          break\n        }\n        // formatted error is an explicit error event (rejections, uncaught errors)\n        case 'formatted-error': {\n          const formattedArgs = await prepareFormattedErrorArgs(\n            entry,\n            ctx,\n            distDir\n          )\n          forwardConsole.error(browserPrefix, ...formattedArgs)\n          break\n        }\n        default: {\n        }\n      }\n    } catch {\n      switch (entry.kind) {\n        case 'any-logged-error': {\n          const consoleArgs = await prepareConsoleErrorArgs(entry, ctx, distDir)\n          forwardConsole.error(browserPrefix, ...consoleArgs)\n          break\n        }\n        case 'console': {\n          const consoleMethod =\n            forwardConsole[entry.method] || forwardConsole.log\n          const consoleArgs = await prepareConsoleArgs(entry, ctx, distDir)\n          ;(consoleMethod as (...args: any[]) => void)(\n            browserPrefix,\n            ...consoleArgs\n          )\n          break\n        }\n        case 'formatted-error': {\n          forwardConsole.error(browserPrefix, `${entry.prefix}\\n`, entry.stack)\n          break\n        }\n        default: {\n        }\n      }\n    }\n  }\n}\n\n// the data is used later when we need to get sourcemaps for error stacks\nexport async function receiveBrowserLogsWebpack(opts: {\n  entries: ServerLogEntry[]\n  router: 'app' | 'pages'\n  sourceType?: 'server' | 'edge-server'\n  clientStats: () => any\n  serverStats: () => any\n  edgeServerStats: () => any\n  rootDirectory: string\n  distDir: string\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n}): Promise<void> {\n  const {\n    entries,\n    router,\n    sourceType,\n    clientStats,\n    serverStats,\n    edgeServerStats,\n    rootDirectory,\n    distDir,\n  } = opts\n\n  const isAppDirectory = router === 'app'\n  const isServer = sourceType === 'server'\n  const isEdgeServer = sourceType === 'edge-server'\n\n  const ctx: MappingContext = {\n    bundler: 'webpack',\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n    clientStats,\n    serverStats,\n    edgeServerStats,\n    rootDirectory,\n  }\n\n  await handleLog(entries, ctx, distDir, opts.config)\n}\n\nexport async function receiveBrowserLogsTurbopack(opts: {\n  entries: ServerLogEntry[]\n  router: 'app' | 'pages'\n  sourceType?: 'server' | 'edge-server'\n  project: Project\n  projectPath: string\n  distDir: string\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n}): Promise<void> {\n  const { entries, router, sourceType, project, projectPath, distDir } = opts\n\n  const isAppDirectory = router === 'app'\n  const isServer = sourceType === 'server'\n  const isEdgeServer = sourceType === 'edge-server'\n\n  const ctx: MappingContext = {\n    bundler: 'turbopack',\n    project,\n    projectPath,\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n  }\n\n  await handleLog(entries, ctx, distDir, opts.config)\n}\n"], "names": ["cyan", "dim", "red", "yellow", "util", "getConsoleLocation", "getSourceMappedStackFrames", "withLocation", "UNDEFINED_MARKER", "restoreUndefined", "x", "undefined", "Array", "isArray", "map", "k", "methods", "methodsToSkipInspect", "Set", "forwardConsole", "console", "Object", "fromEntries", "method", "args", "arg", "has", "inspect", "depth", "Infinity", "colors", "deserializeArgData", "JSON", "parse", "colorError", "mapped", "config", "colorFn", "applyColor", "kind", "prefix", "stack", "frameCode", "processConsoleFormatStrings", "length", "formatString", "includes", "formatted", "format", "stripFormatSpecifiers", "fmtIn", "String", "rest", "slice", "fmtOut", "argPtr", "i", "token", "obj", "result", "push", "prepareFormattedErrorArgs", "entry", "ctx", "distDir", "prepareConsoleArgs", "deserialized", "Promise", "all", "data", "prepareConsoleErrorArgs", "isRejectionMessage", "mappedStack", "consoleErrorStack", "location", "some", "a", "handleTable", "browserPrefix", "deserializedArgs", "consoleMethodStack", "frames", "log", "table", "handleTrace", "mappedIgnored", "handleDir", "loggableEntry", "consoleMethod", "originalWrite", "process", "stdout", "write", "bind", "captured", "chunk", "preserved", "replace", "handleDefaultConsole", "withStackEntry", "original", "handleLog", "entries", "consoleArgs", "error", "formattedArgs", "receiveBrowserLogsWebpack", "opts", "router", "sourceType", "clientStats", "serverStats", "edgeServerStats", "rootDirectory", "isAppDirectory", "isServer", "isEdgeServer", "bundler", "receiveBrowserLogsTurbopack", "project", "projectPath"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QAAQ,0BAAyB;AAEhE,OAAOC,UAAU,OAAM;AACvB,SACEC,kBAAkB,EAClBC,0BAA0B,EAC1BC,YAAY,QAEP,eAAc;AACrB,SAIEC,gBAAgB,QACX,oDAAmD;AAE1D,OAAO,SAASC,iBAAiBC,CAAM;IACrC,IAAIA,MAAMF,kBAAkB,OAAOG;IACnC,IAAIC,MAAMC,OAAO,CAACH,IAAI,OAAOA,EAAEI,GAAG,CAACL;IACnC,IAAIC,KAAK,OAAOA,MAAM,UAAU;QAC9B,IAAK,IAAIK,KAAKL,EAAG;YACfA,CAAC,CAACK,EAAE,GAAGN,iBAAiBC,CAAC,CAACK,EAAE;QAC9B;IACF;IACA,OAAOL;AACT;AAEA,MAAMM,UAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,uBAAuB,IAAIC,IAAI;IACnC;IACA;IACA;IACA;IACA;IACA;CACD;AAED,oHAAoH;AACpH,MAAMC,iBAAiC;IACrC,GAAGC,OAAO;IACV,GAAGC,OAAOC,WAAW,CACnBN,QAAQF,GAAG,CAAC,CAACS,SAAW;YACtBA;YACA,CAAC,GAAGC,OACF,AAACJ,OAAO,CAACG,OAAO,IACXC,KAAKV,GAAG,CAAC,CAACW,MACXR,qBAAqBS,GAAG,CAACH,WACzB,OAAOE,QAAQ,YACfA,QAAQ,OACJA,MAEArB,KAAKuB,OAAO,CAACF,KAAK;wBAAEG,OAAOC;wBAAUC,QAAQ;oBAAK;SAG7D,EACF;AACH;AAEA,eAAeC,mBAAmBN,GAAQ;IACxC,IAAI;QACF,oJAAoJ;QACpJ,IAAIA,QAAQjB,kBAAkB;YAC5B,OAAOC,iBAAiBgB;QAC1B;QAEA,OAAOhB,iBAAiBuB,KAAKC,KAAK,CAACR;IACrC,EAAE,OAAM;QACN,OAAOA;IACT;AACF;AAEA,MAAMS,aAAa,CACjBC,QACAC;IAKA,MAAMC,UACJD,CAAAA,0BAAAA,OAAQE,UAAU,MAAK3B,aAAayB,OAAOE,UAAU,GAAGpC,MAAM,CAAIQ,IAASA;IAC7E,OAAQyB,OAAOI,IAAI;QACjB,KAAK;QACL,KAAK;YAAS;gBACZ,OACE,AAACH,CAAAA,CAAAA,0BAAAA,OAAQI,MAAM,IAAGH,QAAQD,0BAAAA,OAAQI,MAAM,IAAI,EAAC,IAC7C,CAAC,EAAE,EAAEH,QAAQF,OAAOM,KAAK,GAAG;YAEhC;QACA,KAAK;YAAmB;gBACtB,OACE,AAACL,CAAAA,CAAAA,0BAAAA,OAAQI,MAAM,IAAGH,QAAQD,0BAAAA,OAAQI,MAAM,IAAI,EAAC,IAC7C,CAAC,EAAE,EAAEH,QAAQF,OAAOM,KAAK,EAAE,EAAE,EAAEN,OAAOO,SAAS,EAAE;YAErD;QACA,yIAAyI;QACzI,KAAK;YAAe;gBAClB,OAAON,CAAAA,0BAAAA,OAAQI,MAAM,IAAGH,QAAQD,0BAAAA,OAAQI,MAAM,IAAI;YACpD;QACA;YAAS,CACT;IACF;IACAL;AACF;AAEA,SAASQ,4BAA4BnB,IAAW;IAC9C;;;GAGC,GACD,IAAIA,KAAKoB,MAAM,GAAG,KAAK,OAAOpB,IAAI,CAAC,EAAE,KAAK,UAAU;QAClD,MAAMqB,eAAerB,IAAI,CAAC,EAAE;QAC5B,IACEqB,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,OACtB;YACA,IAAI;gBACF,MAAMC,YAAY3C,KAAK4C,MAAM,IAAIxB;gBACjC,OAAO;oBAACuB;iBAAU;YACpB,EAAE,OAAM;gBACN,OAAOvB;YACT;QACF;IACF;IACA,OAAOA;AACT;AAEA,6DAA6D;AAC7D,4DAA4D;AAC5D,0DAA0D;AAC1D,yBAAyB;AACzB,OAAO,SAASyB,sBAAsBzB,IAAW;IAC/C,IAAIA,KAAKoB,MAAM,KAAK,KAAK,OAAOpB,IAAI,CAAC,EAAE,KAAK,UAAU,OAAOA;IAE7D,MAAM0B,QAAQC,OAAO3B,IAAI,CAAC,EAAE;IAC5B,MAAM4B,OAAO5B,KAAK6B,KAAK,CAAC;IAExB,IAAI,CAACH,MAAMJ,QAAQ,CAAC,MAAM,OAAOtB;IAEjC,IAAI8B,SAAS;IACb,IAAIC,SAAS;IAEb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,MAAMN,MAAM,EAAEY,IAAK;QACrC,IAAIN,KAAK,CAACM,EAAE,KAAK,KAAK;YACpBF,UAAUJ,KAAK,CAACM,EAAE;YAClB;QACF;QAEA,IAAIN,KAAK,CAACM,IAAI,EAAE,KAAK,KAAK;YACxBF,UAAU;YACVE;YACA;QACF;QAEA,MAAMC,QAAQP,KAAK,CAAC,EAAEM,EAAE;QAExB,IAAI,CAACC,OAAO;YACVH,UAAU;YACV;QACF;QAEA,IAAI,WAAWR,QAAQ,CAACW,UAAUA,UAAU,KAAK;YAC/C,IAAIF,SAASH,KAAKR,MAAM,EAAE;gBACxB,IAAIa,UAAU,KAAK;oBACjBF;gBACF,OAAO,IAAIE,UAAU,OAAOA,UAAU,OAAOA,UAAU,KAAK;oBAC1D,MAAMC,MAAMN,IAAI,CAACG,SAAS;oBAC1BD,UAAUlD,KAAKuB,OAAO,CAAC+B,KAAK;wBAAE9B,OAAO;wBAAGE,QAAQ;oBAAM;gBACxD,OAAO;oBACL,+CAA+C;oBAC/CwB,UAAUH,OAAOC,IAAI,CAACG,SAAS;gBACjC;YACF;YACA;QACF;QAEAD,UAAU,MAAMG;IAClB;IAEA,MAAME,SAAS;QAACL;KAAO;IACvB,IAAIC,SAASH,KAAKR,MAAM,EAAE;QACxBe,OAAOC,IAAI,IAAIR,KAAKC,KAAK,CAACE;IAC5B;IAEA,OAAOI;AACT;AAEA,eAAeE,0BACbC,KAA2D,EAC3DC,GAAmB,EACnBC,OAAe;IAEf,MAAM7B,SAAS,MAAM7B,2BAA2BwD,MAAMrB,KAAK,EAAEsB,KAAKC;IAClE,OAAO;QAAC9B,WAAWC,QAAQ;YAAEK,QAAQsB,MAAMtB,MAAM;QAAC;KAAG;AACvD;AAEA,eAAeyB,mBACbH,KAAmD,EACnDC,GAAmB,EACnBC,OAAe;IAEf,MAAME,eAAe,MAAMC,QAAQC,GAAG,CACpCN,MAAMtC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIc,IAAI,KAAK,OAAO;YACtB,MAAM8B,OAAO,MAAMtC,mBAAmBN,IAAI4C,IAAI;YAC9C,IAAIP,MAAMvC,MAAM,KAAK,UAAU,OAAO8C,SAAS,UAAU;gBACvD,OAAOlE,OAAOkE;YAChB;YACA,OAAOA;QACT;QACA,IAAI,CAAC5C,IAAIgB,KAAK,EAAE,OAAOvC,IAAIuB,IAAIe,MAAM;QACrC,MAAML,SAAS,MAAM7B,2BAA2BmB,IAAIgB,KAAK,EAAEsB,KAAKC;QAChE,OAAO9B,WAAWC,QAAQ;YAAEK,QAAQf,IAAIe,MAAM;YAAEF,YAAY;QAAM;IACpE;IAGF,OAAOK,4BAA4BuB;AACrC;AAEA,eAAeI,wBACbR,KAA4D,EAC5DC,GAAmB,EACnBC,OAAe;IAEf,MAAME,eAAe,MAAMC,QAAQC,GAAG,CACpCN,MAAMtC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIc,IAAI,KAAK,OAAO;YACtB,IAAId,IAAI8C,kBAAkB,EAAE,OAAOrE,IAAIuB,IAAI4C,IAAI;YAC/C,OAAOtC,mBAAmBN,IAAI4C,IAAI;QACpC;QACA,IAAI,CAAC5C,IAAIgB,KAAK,EAAE,OAAOvC,IAAIuB,IAAIe,MAAM;QACrC,MAAML,SAAS,MAAM7B,2BAA2BmB,IAAIgB,KAAK,EAAEsB,KAAKC;QAChE,OAAO9B,WAAWC,QAAQ;YAAEK,QAAQf,IAAIe,MAAM;QAAC;IACjD;IAGF,MAAMgC,cAAc,MAAMlE,2BACxBwD,MAAMW,iBAAiB,EACvBV,KACAC;IAGF;;;;GAIC,GACD,MAAMU,WAAWrE,mBAAmBmE;IACpC,IAAIV,MAAMtC,IAAI,CAACmD,IAAI,CAAC,CAACC,IAAMA,EAAErC,IAAI,KAAK,wBAAwB;QAC5D,MAAMoB,SAASV,sBAAsBiB;QACrC,IAAIQ,UAAU;YACZf,OAAOC,IAAI,CAAC3D,IAAI,CAAC,CAAC,EAAEyE,SAAS,CAAC,CAAC;QACjC;QACA,OAAOf;IACT;IACA,MAAMA,SAAS;WACVhB,4BAA4BuB;QAC/BhC,WAAWsC;KACZ;IACD,IAAIE,UAAU;QACZf,OAAOC,IAAI,CAAC3D,IAAI,CAAC,CAAC,EAAEyE,SAAS,CAAC,CAAC;IACjC;IACA,OAAOf;AACT;AAEA,eAAekB,YACbf,KAA2B,EAC3BgB,aAAqB,EACrBf,GAAmB,EACnBC,OAAe;IAEf,MAAMe,mBAAmB,MAAMZ,QAAQC,GAAG,CACxCN,MAAMtC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIc,IAAI,KAAK,uBAAuB;YACtC,OAAO;gBAAEE,OAAOhB,IAAIgB,KAAK;YAAC;QAC5B;QACA,OAAOV,mBAAmBN,IAAI4C,IAAI;IACpC;IAGF,MAAMK,WAAW,MAAM,AAAC,CAAA;QACtB,IAAI,CAACZ,MAAMkB,kBAAkB,EAAE;YAC7B;QACF;QACA,MAAMC,SAAS,MAAM3E,2BACnBwD,MAAMkB,kBAAkB,EACxBjB,KACAC;QAEF,OAAO3D,mBAAmB4E;IAC5B,CAAA;IAEA,uFAAuF;IACvF9D,eAAe+D,GAAG,CAACJ;IACnB3D,eAAegE,KAAK,IAAIJ;IACxB,IAAIL,UAAU;QACZvD,eAAe+D,GAAG,CAACjF,IAAI,CAAC,CAAC,EAAEyE,SAAS,CAAC,CAAC;IACxC;AACF;AAEA,eAAeU,YACbtB,KAA2B,EAC3BgB,aAAqB,EACrBf,GAAmB,EACnBC,OAAe;IAEf,MAAMe,mBAAmB,MAAMZ,QAAQC,GAAG,CACxCN,MAAMtC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIc,IAAI,KAAK,uBAAuB;YACtC,IAAI,CAACd,IAAIgB,KAAK,EAAE,OAAOvC,IAAIuB,IAAIe,MAAM;YACrC,MAAML,SAAS,MAAM7B,2BAA2BmB,IAAIgB,KAAK,EAAEsB,KAAKC;YAChE,OAAO9B,WAAWC,QAAQ;gBAAEK,QAAQf,IAAIe,MAAM;YAAC;QACjD;QACA,OAAOT,mBAAmBN,IAAI4C,IAAI;IACpC;IAGF,IAAI,CAACP,MAAMkB,kBAAkB,EAAE;QAC7B7D,eAAe+D,GAAG,CAChBJ,kBACGC,kBACH;QAEF;IACF;IAEA,oHAAoH;IACpH,MAAM,CAAC5C,QAAQkD,cAAc,GAAG,MAAMlB,QAAQC,GAAG,CAAC;QAChD9D,2BAA2BwD,MAAMkB,kBAAkB,EAAEjB,KAAKC,SAAS;QACnE1D,2BAA2BwD,MAAMkB,kBAAkB,EAAEjB,KAAKC;KAC3D;IAED,MAAMU,WAAWrE,mBAAmBgF;IACpClE,eAAe+D,GAAG,CAChBJ,kBACGC,kBACH,CAAC,EAAE,EAAE5C,OAAOM,KAAK,EAAE,KACfiC,WAAW;QAAC,CAAC,EAAE,EAAEzE,IAAI,CAAC,CAAC,EAAEyE,SAAS,CAAC,CAAC,GAAG;KAAC,GAAG,EAAE;AAErD;AAEA,eAAeY,UACbxB,KAA2B,EAC3BgB,aAAqB,EACrBf,GAAmB,EACnBC,OAAe;IAEf,MAAMuB,gBAAgB,MAAMtB,mBAAmBH,OAAOC,KAAKC;IAC3D,MAAMwB,gBACJ,AAACrE,cAAsB,CAAC2C,MAAMvC,MAAM,CAAC,IAAIJ,eAAe+D,GAAG;IAE7D,IAAIpB,MAAMkB,kBAAkB,EAAE;QAC5B,MAAM7C,SAAS,MAAM7B,2BACnBwD,MAAMkB,kBAAkB,EACxBjB,KACAC;QAEF,MAAMU,WAAWzE,IAAI,CAAC,CAAC,EAAEI,mBAAmB8B,QAAQ,CAAC,CAAC;QACtD,MAAMsD,gBAAgBC,QAAQC,MAAM,CAACC,KAAK,CAACC,IAAI,CAACH,QAAQC,MAAM;QAC9D,IAAIG,WAAW;QACfJ,QAAQC,MAAM,CAACC,KAAK,GAAG,CAACG;YACtBD,YAAYC;YACZ,OAAO;QACT;QACA,IAAI;YACFP,iBAAiBD;QACnB,SAAU;YACRG,QAAQC,MAAM,CAACC,KAAK,GAAGH;QACzB;QACA,MAAMO,YAAYF,SAASG,OAAO,CAAC,UAAU;QAC7CR,cAAc,GAAGX,gBAAgBkB,UAAU,CAAC,EAAEtB,SAAS,EAAE,CAAC;QAC1D;IACF;IACAc,cAAcV,kBAAkBS;AAClC;AAEA,eAAeW,qBACbpC,KAA2B,EAC3BgB,aAAqB,EACrBf,GAAmB,EACnBC,OAAe,EACf5B,MAAqE;IAErE,MAAMmD,gBAAgB,MAAMtB,mBAAmBH,OAAOC,KAAKC;IAC3D,MAAMmC,iBAAiB,MAAM5F,aAC3B;QACE6F,UAAUb;QACV9C,OAAO,AAACqB,MAAckB,kBAAkB,IAAI;IAC9C,GACAjB,KACAC,SACA5B;IAEF,MAAMoD,gBAAgBrE,cAAc,CAAC2C,MAAMvC,MAAM,CAAC,IAAIJ,eAAe+D,GAAG;IACtEM,cAA2CV,kBAAkBqB;AACjE;AAEA,OAAO,eAAeE,UACpBC,OAAyB,EACzBvC,GAAmB,EACnBC,OAAe,EACf5B,MAAqE;IAErE,MAAM0C,gBAAgB9E,KAAK;IAE3B,KAAK,MAAM8D,SAASwC,QAAS;QAC3B,IAAI;YACF,OAAQxC,MAAMvB,IAAI;gBAChB,KAAK;oBAAW;wBACd,OAAQuB,MAAMvC,MAAM;4BAClB,KAAK;gCAAS;oCACZ,+CAA+C;oCAC/C,MAAMsD,YAAYf,OAAOgB,eAAef,KAAKC;oCAC7C;gCACF;4BACA,gBAAgB;4BAChB,KAAK;gCAAS;oCACZ,MAAMoB,YAAYtB,OAAOgB,eAAef,KAAKC;oCAC7C;gCACF;4BACA,KAAK;gCAAO;oCACV,MAAMsB,UAAUxB,OAAOgB,eAAef,KAAKC;oCAC3C;gCACF;4BACA,oCAAoC;4BAEpC,wDAAwD;4BACxD,uBAAuB;4BACvB;gCAAS;oCACP,MAAMkC,qBACJpC,OACAgB,eACAf,KACAC,SACA5B;gCAEJ;wBACF;wBACA;oBACF;gBACA,uIAAuI;gBACvI,KAAK;oBAAoB;wBACvB,MAAMmE,cAAc,MAAMjC,wBAAwBR,OAAOC,KAAKC;wBAC9D7C,eAAeqF,KAAK,CAAC1B,kBAAkByB;wBACvC;oBACF;gBACA,2EAA2E;gBAC3E,KAAK;oBAAmB;wBACtB,MAAME,gBAAgB,MAAM5C,0BAC1BC,OACAC,KACAC;wBAEF7C,eAAeqF,KAAK,CAAC1B,kBAAkB2B;wBACvC;oBACF;gBACA;oBAAS,CACT;YACF;QACF,EAAE,OAAM;YACN,OAAQ3C,MAAMvB,IAAI;gBAChB,KAAK;oBAAoB;wBACvB,MAAMgE,cAAc,MAAMjC,wBAAwBR,OAAOC,KAAKC;wBAC9D7C,eAAeqF,KAAK,CAAC1B,kBAAkByB;wBACvC;oBACF;gBACA,KAAK;oBAAW;wBACd,MAAMf,gBACJrE,cAAc,CAAC2C,MAAMvC,MAAM,CAAC,IAAIJ,eAAe+D,GAAG;wBACpD,MAAMqB,cAAc,MAAMtC,mBAAmBH,OAAOC,KAAKC;wBACvDwB,cACAV,kBACGyB;wBAEL;oBACF;gBACA,KAAK;oBAAmB;wBACtBpF,eAAeqF,KAAK,CAAC1B,eAAe,GAAGhB,MAAMtB,MAAM,CAAC,EAAE,CAAC,EAAEsB,MAAMrB,KAAK;wBACpE;oBACF;gBACA;oBAAS,CACT;YACF;QACF;IACF;AACF;AAEA,yEAAyE;AACzE,OAAO,eAAeiE,0BAA0BC,IAU/C;IACC,MAAM,EACJL,OAAO,EACPM,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,eAAe,EACfC,aAAa,EACbjD,OAAO,EACR,GAAG2C;IAEJ,MAAMO,iBAAiBN,WAAW;IAClC,MAAMO,WAAWN,eAAe;IAChC,MAAMO,eAAeP,eAAe;IAEpC,MAAM9C,MAAsB;QAC1BsD,SAAS;QACTF;QACAC;QACAF;QACAJ;QACAC;QACAC;QACAC;IACF;IAEA,MAAMZ,UAAUC,SAASvC,KAAKC,SAAS2C,KAAKvE,MAAM;AACpD;AAEA,OAAO,eAAekF,4BAA4BX,IAQjD;IACC,MAAM,EAAEL,OAAO,EAAEM,MAAM,EAAEC,UAAU,EAAEU,OAAO,EAAEC,WAAW,EAAExD,OAAO,EAAE,GAAG2C;IAEvE,MAAMO,iBAAiBN,WAAW;IAClC,MAAMO,WAAWN,eAAe;IAChC,MAAMO,eAAeP,eAAe;IAEpC,MAAM9C,MAAsB;QAC1BsD,SAAS;QACTE;QACAC;QACAL;QACAC;QACAF;IACF;IAEA,MAAMb,UAAUC,SAASvC,KAAKC,SAAS2C,KAAKvE,MAAM;AACpD", "ignoreList": [0]}